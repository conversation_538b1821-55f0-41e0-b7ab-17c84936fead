<?php

namespace App\Models;

use CodeIgniter\Model;

class MissionModel extends Model
{
    protected $table = 'mission';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'mission_number',
        'mission_name',
        'mission_date',
        'mission_status',
        'user_id',
        'commodity_id',
        'location_id',
        'budgeted_amount',
        'actual_amount',
        'remarks',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'mission_name' => 'required|min_length[3]|max_length[255]',
        'mission_date' => 'required|valid_date',
        'mission_status' => 'required|in_list[pending,in_progress,completed,cancelled]',
        'user_id' => 'permit_empty|integer',
        'commodity_id' => 'permit_empty|integer',
        'location_id' => 'permit_empty|integer',
        'budgeted_amount' => 'permit_empty|decimal',
        'actual_amount' => 'permit_empty|decimal',
        'remarks' => 'permit_empty|max_length[1000]'
    ];

    protected $validationMessages = [
        'mission_name' => [
            'required' => 'Mission name is required.',
            'min_length' => 'Mission name must be at least 3 characters long.',
            'max_length' => 'Mission name cannot exceed 255 characters.'
        ],
        'mission_date' => [
            'required' => 'Mission date is required.',
            'valid_date' => 'Please provide a valid mission date.'
        ],
        'mission_status' => [
            'required' => 'Mission status is required.',
            'in_list' => 'Please select a valid mission status.'
        ],
        'user_id' => [
            'integer' => 'Please select a valid buyer.'
        ],
        'commodity_id' => [
            'integer' => 'Please select a valid commodity.'
        ],
        'location_id' => [
            'integer' => 'Please select a valid location.'
        ],
        'budgeted_amount' => [
            'decimal' => 'Please enter a valid budgeted amount.'
        ],
        'actual_amount' => [
            'decimal' => 'Please enter a valid actual amount.'
        ],
        'remarks' => [
            'max_length' => 'Remarks cannot exceed 1000 characters.'
        ]
    ];

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateMissionNumberCallback'];
    protected $beforeUpdate = [];
    protected $afterInsert = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Generate mission number before insert
     */
    protected function generateMissionNumberCallback(array $data)
    {
        if (!isset($data['data']['mission_number'])) {
            $data['data']['mission_number'] = $this->generateNextMissionNumber();
        }
        return $data;
    }

    /**
     * Generate next mission number
     */
    public function generateNextMissionNumber(): string
    {
        $year = date('Y');
        $lastMission = $this->select('mission_number')
                           ->like('mission_number', $year, 'after')
                           ->orderBy('id', 'DESC')
                           ->first();

        if ($lastMission) {
            $lastNumber = (int) str_replace($year, '', $lastMission['mission_number']);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $nextNumber . $year;
    }

    /**
     * Get mission statistics with caching
     */
    public function getMissionStatistics(): array
    {
        $cacheHelper = new \App\Libraries\CacheHelper();
        $cacheKey = "mission_statistics";

        return $cacheHelper->remember($cacheKey, 600, function() {
            $total = $this->where('deleted_at', null)->countAllResults(false);
            $pending = $this->where('mission_status', 'pending')->where('deleted_at', null)->countAllResults(false);
            $inProgress = $this->where('mission_status', 'in_progress')->where('deleted_at', null)->countAllResults(false);
            $completed = $this->where('mission_status', 'completed')->where('deleted_at', null)->countAllResults(false);
            $cancelled = $this->where('mission_status', 'cancelled')->where('deleted_at', null)->countAllResults(false);

            return [
                'total' => $total,
                'pending' => $pending,
                'in_progress' => $inProgress,
                'completed' => $completed,
                'cancelled' => $cancelled
            ];
        });
    }

    /**
     * Get mission with user and commodity details
     */
    public function getMissionWithDetails(int $id): ?array
    {
        // Use caching for individual mission details
        $cacheHelper = new \App\Libraries\CacheHelper();
        $cacheKey = "mission_details_{$id}";

        return $cacheHelper->remember($cacheKey, 600, function() use ($id) {
            $mission = $this->select('mission.*,
                                     users.fullname as user_name,
                                     users.username,
                                     commodities.commodity_name,
                                     commodities.unit_of_measurement,
                                     locations.location_name,
                                     countries.name as country_name,
                                     provinces.name as province_name,
                                     districts.name as district_name,
                                     llgs.name as llg_name,
                                     CONCAT(countries.name, \' > \',
                                            provinces.name, \' > \',
                                            districts.name, \' > \',
                                            llgs.name) as location_hierarchy')
                           ->join('users', 'users.id = mission.user_id', 'left')
                           ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                           ->join('locations', 'locations.id = mission.location_id', 'left')
                           ->join('countries', 'countries.id = locations.country_id', 'left')
                           ->join('provinces', 'provinces.id = locations.province_id', 'left')
                           ->join('districts', 'districts.id = locations.district_id', 'left')
                           ->join('llgs', 'llgs.id = locations.llg_id', 'left')
                           ->where('mission.id', $id)
                           ->where('mission.deleted_at', null)
                           ->first();

            return $mission ?: null;
        });
    }

    /**
     * Get missions with user and commodity details (with pagination)
     */
    public function getMissionsWithDetailsPaginated(int $perPage = 20, string $search = '', string $status = ''): array
    {
        // Use caching for better performance
        $cacheHelper = new \App\Libraries\CacheHelper();
        $cacheKey = $cacheHelper->generatePaginationKey('missions_paginated', [
            'perPage' => $perPage,
            'search' => $search,
            'status' => $status,
            'page' => $this->request->getGet('page') ?? 1
        ]);

        return $cacheHelper->remember($cacheKey, 300, function() use ($perPage, $search, $status) {
            $query = $this->select('mission.*,
                                    users.fullname as user_name,
                                    users.username,
                                    commodities.commodity_name,
                                    commodities.unit_of_measurement,
                                    locations.location_name')
                         ->join('users', 'users.id = mission.user_id', 'left')
                         ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                         ->join('locations', 'locations.id = mission.location_id', 'left')
                         ->where('mission.deleted_at', null);

            // Apply search filter
            if (!empty($search)) {
                $query->groupStart()
                      ->like('mission.mission_name', $search)
                      ->orLike('mission.mission_number', $search)
                      ->orLike('users.fullname', $search)
                      ->orLike('commodities.commodity_name', $search)
                      ->orLike('locations.location_name', $search)
                      ->groupEnd();
            }

            // Apply status filter
            if (!empty($status)) {
                $query->where('mission.mission_status', $status);
            }

            // Order by latest first
            $query->orderBy('mission.created_at', 'DESC');

            // Get paginated results
            $missions = $query->paginate($perPage);
            $pager = $this->pager;

            return [
                'data' => $missions,
                'pager' => $pager
            ];
        });
    }

    /**
     * Get missions assigned to a specific user
     */
    public function getMissionsByUser(int $userId): array
    {
        return $this->select('mission.*, commodities.commodity_name, commodities.unit_of_measurement')
                   ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                   ->where('mission.user_id', $userId)
                   ->where('mission.deleted_at', null)
                   ->orderBy('mission.mission_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get missions for a specific commodity
     */
    public function getMissionsByCommodity(int $commodityId): array
    {
        return $this->select('mission.*, users.fullname as user_name, users.username')
                   ->join('users', 'users.id = mission.user_id', 'left')
                   ->where('mission.commodity_id', $commodityId)
                   ->where('mission.deleted_at', null)
                   ->orderBy('mission.mission_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get missions for a specific location
     */
    public function getMissionsByLocation(int $locationId): array
    {
        return $this->select('mission.*, 
                             users.fullname as user_name, 
                             users.username,
                             commodities.commodity_name, 
                             commodities.unit_of_measurement')
                   ->join('users', 'users.id = mission.user_id', 'left')
                   ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                   ->where('mission.location_id', $locationId)
                   ->where('mission.deleted_at', null)
                   ->orderBy('mission.mission_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get recent missions
     */
    public function getRecentMissions(int $limit = 10): array
    {
        return $this->select('mission.*, users.fullname as user_name, commodities.commodity_name')
                   ->join('users', 'users.id = mission.user_id', 'left')
                   ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                   ->where('mission.deleted_at', null)
                   ->orderBy('mission.created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get missions by date range
     */
    public function getMissionsByDateRange(string $startDate, string $endDate): array
    {
        return $this->select('mission.*, users.fullname as user_name, commodities.commodity_name')
                   ->join('users', 'users.id = mission.user_id', 'left')
                   ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                   ->where('mission.mission_date >=', $startDate)
                   ->where('mission.mission_date <=', $endDate)
                   ->where('mission.deleted_at', null)
                   ->orderBy('mission.mission_date', 'ASC')
                   ->findAll();
    }



    /**
     * Clear mission-related cache when data changes
     */
    public function clearMissionCache(): void
    {
        $cacheHelper = new \App\Libraries\CacheHelper();

        // Clear specific mission caches
        $cacheHelper->forget('mission_statistics');

        // For a more comprehensive solution, you might want to implement
        // cache tagging to clear all mission-related caches
        // For now, we'll clear the entire cache when missions are modified
        $cacheHelper->flush();
    }

    /**
     * Override insert to clear cache
     */
    public function insert($data = null, bool $returnID = true)
    {
        $result = parent::insert($data, $returnID);
        if ($result) {
            $this->clearMissionCache();
        }
        return $result;
    }

    /**
     * Override update to clear cache
     */
    public function update($id = null, $data = null): bool
    {
        $result = parent::update($id, $data);
        if ($result) {
            $this->clearMissionCache();
        }
        return $result;
    }

    /**
     * Override delete to clear cache
     */
    public function delete($id = null, bool $purge = false)
    {
        $result = parent::delete($id, $purge);
        if ($result) {
            $this->clearMissionCache();
        }
        return $result;
    }
}
