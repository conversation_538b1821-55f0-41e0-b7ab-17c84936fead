<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
    }
    
    .status-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 0.25rem;
    }
    
    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        border-radius: 4px;
    }
    
    .filters-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }
    
    .table-actions {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .bulk-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .table-info {
        color: var(--admin-muted);
        font-size: 0.9rem;
    }
    
    .price {
        font-weight: bold;
        color: #28a745;
    }
    
    .stock-info {
        font-size: 0.85rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header">
    <h1 class="page-title">Buy Commodities</h1>
    <p class="page-description">Browse and purchase agricultural commodities from local farmers.</p>
</div>

<!-- Filters -->
<div class="filters-card">
    <div class="row g-3">
        <div class="col-md-3">
            <label class="form-label">Search Products</label>
            <input type="text" class="form-control" placeholder="Search by name, farmer..." id="searchInput">
        </div>
        <div class="col-md-2">
            <label class="form-label">Category</label>
            <select class="form-select" id="categoryFilter">
                <option value="">All Categories</option>
                <option value="grains">Grains</option>
                <option value="vegetables">Vegetables</option>
                <option value="fruits">Fruits</option>
                <option value="pulses">Pulses</option>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">Availability</label>
            <select class="form-select" id="availabilityFilter">
                <option value="">All</option>
                <option value="in-stock">In Stock</option>
                <option value="low-stock">Low Stock</option>
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">Sort By</label>
            <select class="form-select" id="sortBy">
                <option value="name">Name</option>
                <option value="price">Price</option>
                <option value="stock">Stock</option>
                <option value="date">Date Added</option>
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end gap-2">
            <button type="button" class="btn btn-admin-primary" onclick="applyFilters()">
                <i class="fas fa-filter me-2"></i>Apply Filters
            </button>
            <button type="button" class="btn btn-admin-secondary" onclick="clearFilters()">
                <i class="fas fa-times me-2"></i>Clear
            </button>
        </div>
    </div>
</div>

<!-- Available Commodities -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Available Commodities</h5>
            </div>
            <div class="card-body">
                <div class="admin-table">
                    <table class="table table-hover" id="commoditiesTable">
                        <thead>
                            <tr>
                                <th width="80">Image</th>
                                <th data-sortable>Product Name</th>
                                <th data-sortable>Category</th>
                                <th data-sortable>Farmer</th>
                                <th data-sortable>Price</th>
                                <th data-sortable>Available Stock</th>
                                <th>Status</th>
                                <th>Date Added</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <img src="<?= base_url('public/assets/images/products/rice.jpg') ?>" 
                                         alt="Premium Rice" class="product-image"
                                         onerror="this.src='<?= base_url('public/assets/images/default-product.png') ?>'">
                                </td>
                                <td>
                                    <div class="fw-bold">Premium Basmati Rice</div>
                                    <small class="text-muted">SKU: PRD-001</small>
                                </td>
                                <td><span class="badge bg-info">Grains</span></td>
                                <td>
                                    <div>John Farmer</div>
                                    <small class="text-muted"><EMAIL></small>
                                </td>
                                <td>
                                    <div class="price">$4.50/kg</div>
                                    <small class="text-muted">Min: 10kg</small>
                                </td>
                                <td>
                                    <div class="fw-bold">500kg</div>
                                    <small class="stock-info text-success">In Stock</small>
                                </td>
                                <td><span class="badge bg-success status-badge">Available</span></td>
                                <td>
                                    <div>Jan 15, 2024</div>
                                    <small class="text-muted">2:30 PM</small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewProduct(1)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-admin-primary btn-action" onclick="buyProduct(1)" title="Buy Now">
                                            <i class="fas fa-shopping-cart"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td>
                                    <img src="<?= base_url('public/assets/images/products/wheat.jpg') ?>" 
                                         alt="Organic Wheat" class="product-image"
                                         onerror="this.src='<?= base_url('public/assets/images/default-product.png') ?>'">
                                </td>
                                <td>
                                    <div class="fw-bold">Organic Wheat</div>
                                    <small class="text-muted">SKU: PRD-002</small>
                                </td>
                                <td><span class="badge bg-info">Grains</span></td>
                                <td>
                                    <div>Mike Smith</div>
                                    <small class="text-muted"><EMAIL></small>
                                </td>
                                <td>
                                    <div class="price">$3.20/kg</div>
                                    <small class="text-muted">Min: 25kg</small>
                                </td>
                                <td>
                                    <div class="fw-bold text-warning">50kg</div>
                                    <small class="stock-info text-warning">Low Stock</small>
                                </td>
                                <td><span class="badge bg-warning status-badge">Low Stock</span></td>
                                <td>
                                    <div>Jan 14, 2024</div>
                                    <small class="text-muted">10:15 AM</small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewProduct(2)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-admin-primary btn-action" onclick="buyProduct(2)" title="Buy Now">
                                            <i class="fas fa-shopping-cart"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td>
                                    <img src="<?= base_url('public/assets/images/products/tomatoes.jpg') ?>" 
                                         alt="Fresh Tomatoes" class="product-image"
                                         onerror="this.src='<?= base_url('public/assets/images/default-product.png') ?>'">
                                </td>
                                <td>
                                    <div class="fw-bold">Fresh Tomatoes</div>
                                    <small class="text-muted">SKU: PRD-003</small>
                                </td>
                                <td><span class="badge bg-success">Vegetables</span></td>
                                <td>
                                    <div>Sarah Johnson</div>
                                    <small class="text-muted"><EMAIL></small>
                                </td>
                                <td>
                                    <div class="price">$2.80/kg</div>
                                    <small class="text-muted">Min: 5kg</small>
                                </td>
                                <td>
                                    <div class="fw-bold">200kg</div>
                                    <small class="stock-info text-success">In Stock</small>
                                </td>
                                <td><span class="badge bg-success status-badge">Available</span></td>
                                <td>
                                    <div>Jan 16, 2024</div>
                                    <small class="text-muted">8:45 AM</small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewProduct(3)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-admin-primary btn-action" onclick="buyProduct(3)" title="Buy Now">
                                            <i class="fas fa-shopping-cart"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="table-info">
                        Showing 1-3 of 3 commodities
                    </div>
                    
                    <nav aria-label="Commodities pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize data table functionality
    if (typeof AdminTemplate !== 'undefined' && AdminTemplate.initializeDataTable) {
        AdminTemplate.initializeDataTable('commoditiesTable');
    }
});

function applyFilters() {
    const search = document.getElementById('searchInput').value;
    const category = document.getElementById('categoryFilter').value;
    const availability = document.getElementById('availabilityFilter').value;
    const sortBy = document.getElementById('sortBy').value;
    
    // Implement filtering logic here
    if (typeof AdminTemplate !== 'undefined') {
        AdminTemplate.showNotification('Filters applied successfully!', 'success');
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('availabilityFilter').value = '';
    document.getElementById('sortBy').value = 'name';
    
    if (typeof AdminTemplate !== 'undefined') {
        AdminTemplate.showNotification('Filters cleared!', 'info');
    }
}

function viewProduct(id) {
    // Implement view product logic here
    if (typeof AdminTemplate !== 'undefined') {
        AdminTemplate.showNotification('Viewing product details for ID: ' + id, 'info');
    }
}

function buyProduct(id) {
    // Implement buy product logic here
    if (typeof AdminTemplate !== 'undefined') {
        AdminTemplate.showNotification('Added product to cart. ID: ' + id, 'success');
    }
}
</script>
<?= $this->endSection() ?>