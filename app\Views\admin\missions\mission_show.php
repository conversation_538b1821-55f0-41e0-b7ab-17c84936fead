<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Mission Details -->
<div class="details-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h5 class="section-title mb-0">
            <i class="fas fa-tasks me-2"></i>Mission Information
        </h5>
        <span class="status-badge status-<?= $mission['mission_status'] ?>">
            <?= ucfirst(str_replace('_', ' ', $mission['mission_status'])) ?>
        </span>
    </div>

    <div class="details-grid">
                <!-- Basic Information -->
                <div class="detail-section">
                    <h6 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>Basic Information
                    </h6>
                    
                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-hashtag me-2 text-primary"></i>
                            Mission Number
                        </div>
                        <div class="detail-value">
                            <span class="fw-bold text-primary"><?= esc($mission['mission_number']) ?></span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-tag me-2 text-primary"></i>
                            Mission Name
                        </div>
                        <div class="detail-value">
                            <span class="fw-bold"><?= esc($mission['mission_name']) ?></span>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-calendar me-2 text-primary"></i>
                            Mission Date
                        </div>
                        <div class="detail-value">
                            <?= date('F j, Y', strtotime($mission['mission_date'])) ?>
                            <small class="text-muted">(<?= date('l', strtotime($mission['mission_date'])) ?>)</small>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-flag me-2 text-primary"></i>
                            Status
                        </div>
                        <div class="detail-value">
                            <span class="status-badge status-<?= $mission['mission_status'] ?>">
                                <?= ucfirst(str_replace('_', ' ', $mission['mission_status'])) ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Assignment Information -->
                <div class="detail-section">
                    <h6 class="section-title">
                        <i class="fas fa-users me-2"></i>Assignment Information
                    </h6>
                    
                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-user me-2 text-primary"></i>
                            Assigned Buyer
                        </div>
                        <div class="detail-value">
                            <?php if (!empty($mission['user_name'])): ?>
                                <div class="fw-bold"><?= esc($mission['user_name']) ?></div>
                                <small class="text-muted">@<?= esc($mission['username']) ?></small>
                            <?php else: ?>
                                <span class="text-muted">Not assigned</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-box me-2 text-primary"></i>
                            Commodity
                        </div>
                        <div class="detail-value">
                            <?php if (!empty($mission['commodity_name'])): ?>
                                <div class="fw-bold"><?= esc($mission['commodity_name']) ?></div>
                                <small class="text-muted">Unit: <?= esc($mission['unit_of_measurement']) ?></small>
                            <?php else: ?>
                                <span class="text-muted">Not specified</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                            Mission Location
                        </div>
                        <div class="detail-value">
                            <?php if (!empty($mission['location_name'])): ?>
                                <!-- Primary Location -->
                                <div class="mb-3">
                                    <h6 class="mb-2 text-primary">
                                        <i class="fas fa-map-pin me-2"></i><?= esc($mission['location_name']) ?>
                                    </h6>
                                </div>
                                
                                <!-- Location Hierarchy -->
                                <div class="location-hierarchy">
                                    <h6 class="mb-2 text-muted">
                                        <i class="fas fa-sitemap me-2"></i>Location Hierarchy
                                    </h6>
                                    <div class="hierarchy-path">
                                        <?php if (!empty($mission['country_name'])): ?>
                                            <div class="hierarchy-item">
                                                <i class="fas fa-globe me-2 text-info"></i>
                                                <span class="hierarchy-label">Country:</span>
                                                <span class="hierarchy-value"><?= esc($mission['country_name']) ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($mission['province_name'])): ?>
                                            <div class="hierarchy-item">
                                                <i class="fas fa-map me-2 text-success"></i>
                                                <span class="hierarchy-label">Province:</span>
                                                <span class="hierarchy-value"><?= esc($mission['province_name']) ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($mission['district_name'])): ?>
                                            <div class="hierarchy-item">
                                                <i class="fas fa-map-marked me-2 text-warning"></i>
                                                <span class="hierarchy-label">District:</span>
                                                <span class="hierarchy-value"><?= esc($mission['district_name']) ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($mission['llg_name'])): ?>
                                            <div class="hierarchy-item">
                                                <i class="fas fa-building me-2 text-danger"></i>
                                                <span class="hierarchy-label">LLG:</span>
                                                <span class="hierarchy-value"><?= esc($mission['llg_name']) ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Full Path -->
                                    <?php if (!empty($mission['location_hierarchy'])): ?>
                                        <div class="full-path mt-3">
                                            <small class="text-muted">
                                                <i class="fas fa-route me-2"></i>
                                                <strong>Full Path:</strong> <?= esc($mission['location_hierarchy']) ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">Location not specified</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Financial Information -->
                <div class="detail-section">
                    <h6 class="section-title">
                        <i class="fas fa-dollar-sign me-2"></i>Financial Information
                    </h6>
                    
                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-calculator me-2 text-primary"></i>
                            Budgeted Amount
                        </div>
                        <div class="detail-value">
                            <?php if (!empty($mission['budgeted_amount'])): ?>
                                <span class="fw-bold text-success">$<?= number_format($mission['budgeted_amount'], 2) ?></span>
                            <?php else: ?>
                                <span class="text-muted">Not specified</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-receipt me-2 text-primary"></i>
                            Actual Amount
                        </div>
                        <div class="detail-value">
                            <?php if (!empty($mission['actual_amount'])): ?>
                                <span class="fw-bold text-info">$<?= number_format($mission['actual_amount'], 2) ?></span>
                                
                                <?php if (!empty($mission['budgeted_amount'])): ?>
                                    <?php
                                    $variance = $mission['actual_amount'] - $mission['budgeted_amount'];
                                    $variancePercent = ($variance / $mission['budgeted_amount']) * 100;
                                    $varianceClass = $variance > 0 ? 'text-danger' : ($variance < 0 ? 'text-success' : 'text-muted');
                                    ?>
                                    <div class="mt-2">
                                        <small class="<?= $varianceClass ?>">
                                            <i class="fas fa-chart-line me-1"></i>
                                            Variance: <?= $variance > 0 ? '+' : '' ?>$<?= number_format($variance, 2) ?>
                                            (<?= $variance > 0 ? '+' : '' ?><?= number_format($variancePercent, 1) ?>%)
                                        </small>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="text-muted">Not recorded</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="detail-section">
                    <h6 class="section-title">
                        <i class="fas fa-sticky-note me-2"></i>Additional Information
                    </h6>
                    
                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-comment me-2 text-primary"></i>
                            Remarks
                        </div>
                        <div class="detail-value">
                            <?php if (!empty($mission['remarks'])): ?>
                                <div class="remarks-content">
                                    <?= nl2br(esc($mission['remarks'])) ?>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">No remarks</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-clock me-2 text-primary"></i>
                            Created
                        </div>
                        <div class="detail-value">
                            <?= date('F j, Y \a\t g:i A', strtotime($mission['created_at'])) ?>
                        </div>
                    </div>

                    <?php if (!empty($mission['updated_at']) && $mission['updated_at'] !== $mission['created_at']): ?>
                    <div class="detail-row">
                        <div class="detail-label">
                            <i class="fas fa-edit me-2 text-primary"></i>
                            Last Updated
                        </div>
                        <div class="detail-value">
                            <?= date('F j, Y \a\t g:i A', strtotime($mission['updated_at'])) ?>
                        </div>
                    </div>
                    <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.details-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.detail-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.section-title {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    margin-bottom: 1rem;
    align-items: flex-start;
}

.detail-label {
    flex: 0 0 40%;
    font-weight: 600;
    color: #6c757d;
    padding-right: 1rem;
}

.detail-value {
    flex: 1;
    color: #212529;
}

.location-hierarchy {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
}

.hierarchy-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.hierarchy-label {
    font-weight: 600;
    margin-right: 0.5rem;
    min-width: 80px;
}

.hierarchy-value {
    color: #495057;
}

.full-path {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 0.75rem;
    border-left: 3px solid #007bff;
}

.remarks-content {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.75rem;
    line-height: 1.6;
}

.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-in_progress {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #a3d9a5;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f1aeb5;
}

@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .detail-row {
        flex-direction: column;
    }
    
    .detail-label {
        flex: none;
        margin-bottom: 0.25rem;
        padding-right: 0;
    }
}
</style>
<?= $this->endSection() ?>
