# DCBuyer - Commodity Management System - Technical Design

## System Overview
DCBuyer is a comprehensive web application for managing commodity buying operations with user management, mission tracking, commodity management, and transaction processing. Built with CodeIgniter 4 and Supabase PostgreSQL for modern, scalable operations.

## Current Technology Stack

### Backend Framework
- **CodeIgniter 4** - PHP MVC framework
- **PHP 8.1+** - Server-side scripting language
- **Apache/Nginx** - Web server
- **Composer** - PHP dependency management

### Database
- **Supabase PostgreSQL** - Managed cloud database
- **PostgreSQL 15+** - Relational database with JSONB support
- **Connection pooling** and SSL encryption
- **Automated backups** and point-in-time recovery

### Frontend
- **HTML5** with semantic markup and accessibility
- **CSS3** with Bootstrap 5 framework
- **JavaScript (ES6+)** for client-side interactivity
- **Responsive design** for mobile compatibility

### Authentication & Security
- **Session-based authentication** with CodeIgniter 4 sessions
- **CSRF protection** on all form submissions
- **Role-based access control** (Admin, Supervisor, Buyer)
- **Input validation** and sanitization
- **Audit logging** for sensitive operations

## Database Schema (PostgreSQL)

### Core Tables

#### 1. Users Table
```sql
-- User status enum
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended', 'pending');

CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    fullname TEXT NOT NULL,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    is_buyer BOOLEAN NOT NULL DEFAULT FALSE,
    is_supervisor BOOLEAN NOT NULL DEFAULT FALSE,
    reports_to BIGINT REFERENCES users(id),
    status user_status NOT NULL DEFAULT 'active',
    remarks TEXT,
    created_by BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by BIGINT,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_by BIGINT,
    deleted_at TIMESTAMP
);

-- Indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 2. Commodities Table
```sql
CREATE TABLE commodities (
    commodity_id SERIAL PRIMARY KEY,
    commodity_name VARCHAR(255) NOT NULL,
    unit_of_measurement VARCHAR(50) NOT NULL, -- kg, tons, bags, liters, etc.
    remarks TEXT,
    created_by INT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by INT,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_by INT,
    deleted_at TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Indexes
CREATE INDEX idx_commodities_name ON commodities(commodity_name);
CREATE INDEX idx_commodities_unit ON commodities(unit_of_measurement);
CREATE INDEX idx_commodities_created_at ON commodities(created_at);
CREATE INDEX idx_commodities_is_deleted ON commodities(is_deleted);
```

#### 3. Missions Table (Simplified Assignment System)
```sql
-- Mission status enum
CREATE TYPE mission_status AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');

CREATE TABLE mission (
    id SERIAL PRIMARY KEY,
    mission_number VARCHAR(20) UNIQUE NOT NULL, -- Auto-generated: {increment}{year} e.g., 12025, 22025
    mission_name VARCHAR(255) NOT NULL,
    mission_date DATE NOT NULL,
    mission_status mission_status NOT NULL DEFAULT 'pending',

    -- Simplified Assignment Fields (One Mission = One Buyer + One Commodity)
    user_id BIGINT REFERENCES users(id), -- assigned buyer
    commodity_id BIGINT REFERENCES commodities(commodity_id), -- assigned commodity
    budgeted_amount DECIMAL(15,2), -- budgeted amount for this mission
    actual_amount DECIMAL(15,2) DEFAULT 0.00, -- actual amount spent/achieved

    remarks TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by INT,
    deleted_at TIMESTAMP,
    deleted_by INT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,

    -- Foreign Key Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (commodity_id) REFERENCES commodities(commodity_id) ON DELETE SET NULL
);

-- Indexes
CREATE INDEX idx_mission_number ON mission(mission_number);
CREATE INDEX idx_mission_name ON mission(mission_name);
CREATE INDEX idx_mission_date ON mission(mission_date);
CREATE INDEX idx_mission_status ON mission(mission_status);
CREATE INDEX idx_mission_user_id ON mission(user_id);
CREATE INDEX idx_mission_commodity_id ON mission(commodity_id);
CREATE INDEX idx_mission_budgeted_amount ON mission(budgeted_amount);
CREATE INDEX idx_mission_created_at ON mission(created_at);
CREATE INDEX idx_mission_deleted_at ON mission(deleted_at);
CREATE INDEX idx_mission_is_deleted ON mission(is_deleted);
```

### Location Management Tables

#### 4. Countries Table
```sql
CREATE TABLE countries (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(3) UNIQUE NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_countries_name ON countries(name);
CREATE INDEX idx_countries_code ON countries(code);
```

#### 5. Provinces Table
```sql
CREATE TABLE provinces (
    id SERIAL PRIMARY KEY,
    country_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX idx_provinces_country_id ON provinces(country_id);
CREATE INDEX idx_provinces_name ON provinces(name);
```

#### 6. Districts Table
```sql
CREATE TABLE districts (
    id SERIAL PRIMARY KEY,
    province_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (province_id) REFERENCES provinces(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX idx_districts_province_id ON districts(province_id);
CREATE INDEX idx_districts_name ON districts(name);
```

#### 7. LLGs Table (Local Level Governments)
```sql
CREATE TABLE llgs (
    id SERIAL PRIMARY KEY,
    district_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX idx_llgs_district_id ON llgs(district_id);
CREATE INDEX idx_llgs_name ON llgs(name);
```

#### 8. Locations Table
```sql
CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    country_id BIGINT NOT NULL,
    province_id BIGINT NOT NULL,
    district_id BIGINT NOT NULL,
    llg_id BIGINT NOT NULL,
    ward VARCHAR(100) NOT NULL, -- User input
    location_name VARCHAR(100) NOT NULL, -- User input
    gps_latitude DECIMAL(10, 8), -- User input
    gps_longitude DECIMAL(11, 8), -- User input
    remarks TEXT, -- User input
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by BIGINT,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_by BIGINT,
    deleted_at TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY (country_id) REFERENCES countries(id),
    FOREIGN KEY (province_id) REFERENCES provinces(id),
    FOREIGN KEY (district_id) REFERENCES districts(id),
    FOREIGN KEY (llg_id) REFERENCES llgs(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Indexes
CREATE INDEX idx_locations_country_id ON locations(country_id);
CREATE INDEX idx_locations_province_id ON locations(province_id);
CREATE INDEX idx_locations_district_id ON locations(district_id);
CREATE INDEX idx_locations_llg_id ON locations(llg_id);
CREATE INDEX idx_locations_ward ON locations(ward);
CREATE INDEX idx_locations_name ON locations(location_name);
CREATE INDEX idx_locations_created_at ON locations(created_at);
CREATE INDEX idx_locations_is_deleted ON locations(is_deleted);
```

### Customer Management Tables

#### 9. Customers Table
```sql
CREATE TABLE customers (
    id SERIAL PRIMARY KEY,
    customer_code VARCHAR(20) UNIQUE NOT NULL, -- Auto-generated unique code
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    location_id BIGINT,
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by BIGINT,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_by BIGINT,
    deleted_at TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY (location_id) REFERENCES locations(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Indexes
CREATE INDEX idx_customers_code ON customers(customer_code);
CREATE INDEX idx_customers_name ON customers(first_name, last_name);
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_location_id ON customers(location_id);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_created_at ON customers(created_at);
CREATE INDEX idx_customers_is_deleted ON customers(is_deleted);
```

### Transaction Management Tables

#### 10. Buy Transactions Table
```sql
CREATE TABLE buy_transactions (
    id SERIAL PRIMARY KEY,
    transaction_code VARCHAR(30) UNIQUE NOT NULL, -- Auto-generated
    user_id BIGINT NOT NULL,
    mission_id BIGINT NOT NULL,
    commodity_id BIGINT NOT NULL,
    customer_id BIGINT NULL, -- Can be empty
    location_id BIGINT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    payment_amount DECIMAL(15,2) NOT NULL,
    transaction_date DATE NOT NULL,
    remarks TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, completed, cancelled
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by BIGINT,
    deleted_at TIMESTAMP,
    deleted_by BIGINT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (mission_id) REFERENCES mission(id),
    FOREIGN KEY (commodity_id) REFERENCES commodities(commodity_id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (location_id) REFERENCES locations(id)
);

-- Indexes
CREATE INDEX idx_buy_transactions_code ON buy_transactions(transaction_code);
CREATE INDEX idx_buy_transactions_user_id ON buy_transactions(user_id);
CREATE INDEX idx_buy_transactions_mission_id ON buy_transactions(mission_id);
CREATE INDEX idx_buy_transactions_commodity_id ON buy_transactions(commodity_id);
CREATE INDEX idx_buy_transactions_customer_id ON buy_transactions(customer_id);
CREATE INDEX idx_buy_transactions_location_id ON buy_transactions(location_id);
CREATE INDEX idx_buy_transactions_date ON buy_transactions(transaction_date);
CREATE INDEX idx_buy_transactions_status ON buy_transactions(status);
CREATE INDEX idx_buy_transactions_created_at ON buy_transactions(created_at);
CREATE INDEX idx_buy_transactions_is_deleted ON buy_transactions(is_deleted);
```



## Current Implementation Status & Features

### ✅ Implemented Features

#### 1. User Management System
- **Complete CRUD Operations**: Create, read, update, soft delete
- **Role-Based Access Control**: Admin, Supervisor, Buyer roles
- **Hierarchical Structure**: Users can report to supervisors
- **Session Authentication**: Secure login/logout system
- **User Dashboard**: Personalized interface based on role
- **Search & Filtering**: Advanced user search capabilities
- **Audit Trail**: Created/updated/deleted by tracking

#### 2. Commodity Management System
- **Full CRUD Operations**: Complete commodity lifecycle management
- **Unit Standardization**: Consistent measurement units (kg, tons, bags, etc.)
- **Search Functionality**: Quick commodity lookup
- **Soft Delete**: Data preservation with logical deletion
- **Audit Logging**: Complete change tracking
- **Responsive Interface**: Mobile-friendly design

#### 3. Mission Management System (Simplified Assignment)
- **Auto-Generated Mission Numbers**: Format {increment}{year} (e.g., 12025, 22025)
- **Mission Lifecycle**: Pending → In Progress → Completed/Cancelled
- **Date Management**: Mission scheduling and tracking
- **Search & Filter**: By mission number, name, status, date, buyer, commodity
- **Statistics Dashboard**: Mission counts by status
- **Comprehensive CRUD**: Full mission management capabilities
- **Simplified Assignment**: Each mission assigned to one buyer for one commodity
- **Budget Management**: Direct budget allocation and actual amount tracking
- **Multiple Commodity Support**: Create separate missions for each commodity per buyer
- **Transaction Authorization**: Users can only transact if assigned to mission
- **Audit Trail**: Complete change history

### 🔄 Planned Features (To Be Implemented)

#### 5. Location Management System
- **Hierarchical Structure**: Country → Province → District → LLG → Ward → Location
- **Dropdown Cascading**: Dynamic AJAX-powered location selection
- **GPS Integration**: Store latitude/longitude coordinates for precise tracking
- **User Input Fields**: Ward, location name, GPS coordinates, and remarks
- **CSV Bulk Import**: Simple CSV import for countries, provinces, districts, LLGs (no logging table needed)
- **Search & Filter**: Advanced location search capabilities

#### 6. Customer Management System
- **Auto-Generated Codes**: Unique customer identification system
- **Contact Management**: Phone, email, address tracking
- **Location Linking**: Associate customers with specific geographic locations
- **Status Management**: Active/inactive customer status tracking
- **Search Functionality**: Quick customer lookup by code, name, or contact info
- **Transaction History**: View customer's transaction history

#### 7. Buy Transaction Management System
- **Authorization Validation**: Verify user is assigned to mission before allowing transactions
- **Commodity Verification**: Ensure user is assigned to specific commodity in mission
- **Real-time Budget Updates**: Automatically update actual_amount in assignment table
- **Transaction Tracking**: Complete transaction lifecycle management
- **Optional Customer**: Transactions can be made without customer assignment
- **Location Requirement**: All transactions must be linked to a location
- **Payment Calculations**: Quantity × Unit Price = Payment Amount validation

### 🔄 Architecture Patterns

#### RESTful API Design (CodeIgniter 4)
- **RESTful Routes**: Standard HTTP methods (GET, POST, PUT, DELETE)
- **Resource-Based URLs**: `/admin/users`, `/admin/missions`, `/admin/commodities`
- **HTTP Status Codes**: Proper response codes for different scenarios
- **JSON Responses**: Structured API responses for AJAX calls
- **Request Validation**: Input validation following REST principles

#### MVC Architecture (CodeIgniter 4)
- **Models**: Data access layer with business logic and relationships
- **Views**: Presentation layer with Bootstrap 5 responsive UI
- **Controllers**: RESTful request handling and business flow
- **Services**: Shared business logic and utilities

#### Database Design Patterns
- **Soft Delete**: Logical deletion with `is_deleted` flag across all tables
- **Audit Trail**: Created/updated/deleted by tracking with user references
- **Timestamp Tracking**: Automatic timestamp management
- **Foreign Key Constraints**: Data integrity with CASCADE options
- **Junction Tables**: Many-to-many relationships (user_commodity_mission)
- **Unique Constraints**: Prevent duplicate data (mission numbers, customer codes)
- **Indexing Strategy**: Performance optimization on frequently queried fields

#### Security Patterns
- **CSRF Protection**: All forms protected against CSRF attacks
- **Input Validation**: Server-side validation with CodeIgniter rules
- **SQL Injection Prevention**: Query builder and prepared statements
- **Session Security**: Secure session management with role-based access
- **Authorization Middleware**: Transaction authorization based on mission assignments
- **Data Sanitization**: Output escaping and input cleaning

## Business Rules & Validation Logic

### Mission Assignment Rules (Simplified System)
1. **One-to-One Assignment**: Each mission is assigned to exactly one buyer for exactly one commodity
2. **Multiple Commodities**: If a buyer handles multiple commodities, create separate missions
3. **Budget Validation**: Budget must be greater than 0 for each mission
4. **Mission Status**: Only active buyers can be assigned to missions
5. **User Status**: Only active users can be assigned to missions
6. **Commodity Status**: Only active commodities can be assigned to missions

### Transaction Authorization Rules
1. **Mission Assignment**: User must be the assigned buyer for the mission to create transactions
2. **Commodity Match**: Transaction commodity must match the mission's assigned commodity
3. **Mission Status**: Transactions only allowed for active missions (in_progress status)
4. **Budget Limits**: Transaction amounts should not exceed mission budget (warning system)
5. **Location Requirement**: All transactions must have a valid location

### Data Integrity Rules
1. **Soft Delete**: All deletions are logical (is_deleted = true) to preserve data integrity
2. **Audit Trail**: All changes tracked with user ID and timestamp
3. **Unique Constraints**: Mission numbers, customer codes must be unique
4. **Foreign Key Integrity**: All relationships maintained with proper constraints
5. **Status Validation**: Enum values enforced at database and application level

### Auto-Generation Rules
1. **Mission Numbers**: Format {increment}{year} - auto-generated on creation
2. **Customer Codes**: Unique alphanumeric codes auto-generated
3. **Transaction Codes**: Unique transaction identifiers auto-generated
4. **Timestamps**: Automatic creation and update timestamp management

### Search & Filter Logic
1. **Mission Search**: By mission number, name, status, date range
2. **User Search**: By name, username, email, role, status
3. **Commodity Search**: By name, unit of measurement
4. **Transaction Search**: By transaction code, user, mission, commodity, date range
5. **Customer Search**: By customer code, name, phone, email

## RESTful Route Structure (CodeIgniter 4)

### Authentication Routes
- `GET /login` - Login form
- `POST /login` - Process login
- `GET /logout` - Logout user
- `GET /dashboard` - User dashboard

### Admin Routes (RESTful Design)

#### User Management (RESTful CRUD)
- `GET /admin/users` - List all users (index)
- `GET /admin/users/create` - Create user form (create)
- `POST /admin/users` - Store new user (store)
- `GET /admin/users/{id}` - View user details (show)
- `GET /admin/users/{id}/edit` - Edit user form (edit)
- `PUT /admin/users/{id}` - Update user (update)
- `DELETE /admin/users/{id}` - Soft delete user (destroy)

#### Commodity Management (RESTful CRUD)
- `GET /admin/commodities` - List all commodities (index)
- `GET /admin/commodities/create` - Create commodity form (create)
- `POST /admin/commodities` - Store new commodity (store)
- `GET /admin/commodities/{id}` - View commodity details (show)
- `GET /admin/commodities/{id}/edit` - Edit commodity form (edit)
- `PUT /admin/commodities/{id}` - Update commodity (update)
- `DELETE /admin/commodities/{id}` - Soft delete commodity (destroy)

#### Mission Management (RESTful CRUD with Simplified Assignment)
- `GET /admin/missions` - List all missions with buyer and commodity info (index)
- `GET /admin/missions/create` - Create mission form with buyer/commodity selection (create)
- `POST /admin/missions` - Store new mission with buyer/commodity assignment (store)
- `GET /admin/missions/{id}` - View mission details with assignment info (show)
- `GET /admin/missions/{id}/edit` - Edit mission form with buyer/commodity selection (edit)
- `PUT /admin/missions/{id}` - Update mission with assignment changes (update)
- `DELETE /admin/missions/{id}` - Soft delete mission (destroy)

#### Location Management (RESTful CRUD)
- `GET /admin/locations` - List all locations (index)
- `GET /admin/locations/create` - Create location form (create)
- `POST /admin/locations` - Store new location (store)
- `GET /admin/locations/{id}` - View location details (show)
- `GET /admin/locations/{id}/edit` - Edit location form (edit)
- `PUT /admin/locations/{id}` - Update location (update)
- `DELETE /admin/locations/{id}` - Soft delete location (destroy)

#### Location Hierarchy API (AJAX)
- `GET /api/provinces/{country_id}` - Get provinces by country
- `GET /api/districts/{province_id}` - Get districts by province
- `GET /api/llgs/{district_id}` - Get LLGs by district

#### Customer Management (RESTful CRUD)
- `GET /admin/customers` - List all customers (index)
- `GET /admin/customers/create` - Create customer form (create)
- `POST /admin/customers` - Store new customer with auto-generated code (store)
- `GET /admin/customers/{id}` - View customer details (show)
- `GET /admin/customers/{id}/edit` - Edit customer form (edit)
- `PUT /admin/customers/{id}` - Update customer (update)
- `DELETE /admin/customers/{id}` - Soft delete customer (destroy)

#### Buy Transaction Management (RESTful CRUD)
- `GET /admin/transactions` - List all transactions (index)
- `GET /admin/transactions/create` - Create transaction form (create)
- `POST /admin/transactions` - Store new transaction (store)
- `GET /admin/transactions/{id}` - View transaction details (show)
- `GET /admin/transactions/{id}/edit` - Edit transaction form (edit)
- `PUT /admin/transactions/{id}` - Update transaction (update)
- `DELETE /admin/transactions/{id}` - Soft delete transaction (destroy)

#### Transaction Authorization API
- `GET /api/user-missions/{user_id}` - Get missions assigned to user as buyer
- `GET /api/mission-details/{mission_id}` - Get mission details including assigned buyer and commodity

#### Bulk Import Management (Location Data)
- `GET /admin/import` - Import dashboard for location data
- `POST /admin/import/countries` - Import countries CSV
- `POST /admin/import/provinces` - Import provinces CSV
- `POST /admin/import/districts` - Import districts CSV
- `POST /admin/import/llgs` - Import LLGs CSV

## Security Implementation

### Current Security Measures
1. **Session-Based Authentication**: CodeIgniter 4 secure sessions
2. **CSRF Protection**: All forms include CSRF tokens
3. **Role-Based Access Control**: Admin, Supervisor, Buyer permissions
4. **Input Validation**: Server-side validation with CodeIgniter rules
5. **SQL Injection Prevention**: Query builder and prepared statements
6. **XSS Protection**: Output escaping in all views
7. **Audit Trail**: Complete user action logging
8. **Secure File Upload**: Validated file types and secure storage

### Database Security
- **Supabase Security**: Row Level Security (RLS) policies
- **SSL Encryption**: All database connections encrypted
- **Connection Pooling**: Managed database connections
- **Backup Strategy**: Automated daily backups with point-in-time recovery

## Performance Optimizations

### Current Optimizations
1. **Database Indexing**: Strategic indexes on frequently queried fields
2. **Pagination**: Implemented on all list views (20 items per page)
3. **Soft Delete Strategy**: Maintains data integrity while improving performance
4. **Query Optimization**: Efficient database queries with proper joins
5. **Asset Optimization**: Minified CSS/JS, optimized images
6. **Caching Strategy**: Browser caching for static assets

### Future Optimizations
- **Redis Caching**: For session data and frequently accessed data
- **CDN Integration**: For static asset delivery
- **Database Query Caching**: For complex reporting queries
- **API Rate Limiting**: For future API endpoints

## Development Environment

### Local Development Setup
- **XAMPP**: Apache, MySQL, PHP development stack
- **Base URL Access**: `http://localhost/dcbuyer/`
- **Database**: Supabase PostgreSQL cloud database
- **File Storage**: Local `public/uploads/` directory
- **Version Control**: Git with GitHub repository

### Deployment Considerations
- **Production Server**: Apache/Nginx with PHP 8.1+
- **Database**: Supabase PostgreSQL (production-ready)
- **SSL Certificate**: Required for production deployment
- **Environment Configuration**: Separate configs for dev/staging/production
- **File Storage**: Consider cloud storage for production file uploads

## Code Quality Standards

### Naming Conventions
- **View Files**: Prefixed with feature name (e.g., `mission_index.php`, `commodity_create.php`)
- **Controllers**: RESTful naming with proper HTTP methods
- **Models**: Singular naming with proper relationships
- **Database**: Snake_case for tables and columns

### Development Practices
- **MVC Separation**: Clear separation of concerns
- **DRY Principle**: Reusable components and functions
- **Error Handling**: Comprehensive error handling and user feedback
- **Code Documentation**: Inline comments and method documentation
- **Testing Strategy**: Manual testing with user feedback loops

## Current File Structure

```
dcbuyer/
├── app/
│   ├── Controllers/
│   │   ├── AuthController.php              # Authentication logic
│   │   ├── DashboardController.php         # Dashboard functionality
│   │   ├── UserController.php              # User management (RESTful)
│   │   ├── CommodityController.php         # Commodity management (RESTful)
│   │   ├── MissionController.php           # Mission management with simplified assignment (RESTful)
│   │   ├── LocationController.php          # Location management (RESTful)
│   │   ├── CustomerController.php          # Customer management (RESTful)
│   │   ├── TransactionController.php       # Buy transaction management (RESTful)
│   │   ├── ImportController.php            # CSV bulk import functionality
│   │   └── ApiController.php               # API endpoints for AJAX calls
│   ├── Models/
│   │   ├── UserModel.php                   # User data access and relationships
│   │   ├── CommodityModel.php              # Commodity data access
│   │   ├── MissionModel.php                # Mission data access with buyer/commodity assignment
│   │   ├── CountryModel.php                # Country data access
│   │   ├── ProvinceModel.php               # Province data access
│   │   ├── DistrictModel.php               # District data access
│   │   ├── LlgModel.php                    # LLG data access
│   │   ├── LocationModel.php               # Location data access
│   │   ├── CustomerModel.php               # Customer data access with auto-coding
│   │   └── BuyTransactionModel.php         # Transaction data access
│   ├── Views/
│   │   ├── auth/                           # Authentication views
│   │   ├── dashboard/                      # Dashboard views
│   │   ├── admin/
│   │   │   ├── users/                      # User management views
│   │   │   ├── commodities/                # Commodity management views
│   │   │   ├── missions/                   # Mission management views with assignment
│   │   │   ├── locations/                  # Location management views
│   │   │   ├── customers/                  # Customer management views
│   │   │   ├── transactions/               # Transaction management views
│   │   │   └── import/                     # Bulk import views
│   │   └── templates/
│   │       └── admin_template.php          # Main admin layout
│   ├── Config/
│   │   ├── Routes.php                      # RESTful application routes
│   │   ├── Database.php                    # Supabase PostgreSQL configuration
│   │   └── App.php                         # Application configuration
│   └── Database/
│       └── PostgreSQL/                     # Database schema files
├── public/
│   ├── uploads/                            # File upload directory
│   ├── assets/                             # CSS, JS, images
│   └── index.php                           # Application entry point
├── writable/                               # Logs and cache
└── vendor/                                 # Composer dependencies
```

## Future Development Roadmap

### Phase 1: Core Foundation (Current)
- ✅ User Management System with Role-Based Access
- ✅ Commodity Management System
- ✅ Mission Management System with Auto-numbering
- ✅ Basic Authentication & Authorization
- ✅ RESTful Architecture Implementation

### Phase 2: Location Management (Next)
- 🔄 Location Management System (Countries → Provinces → Districts → LLGs)
- 🔄 Hierarchical Location Dropdowns with AJAX
- 🔄 Simple CSV Bulk Import for Location Data (Countries, Provinces, Districts, LLGs)
- 🔄 Location-based GPS Tracking

### Phase 3: Customer & Transaction Management
- 📋 Customer Management System with Auto-generated Codes
- 📋 Buy Transaction System with Authorization Checks
- 📋 Real-time Budget Tracking & Updates
- 📋 Transaction Validation & Processing
- 📋 Payment Amount Calculations

### Phase 4: Advanced Features & Analytics
- 📋 Dashboard Analytics & Reporting
- 📋 Mission Performance Metrics
- 📋 Budget vs Actual Analysis
- 📋 Transaction History & Trends
- 📋 Export Capabilities (PDF, Excel)
- 📋 Advanced Search & Filtering

### Phase 5: Integration & Optimization
- 📋 Mobile-Responsive Enhancements
- 📋 API Development for External Systems
- 📋 Third-party Integrations
- 📋 Performance Optimization
- 📋 Advanced Security Features
- 📋 Automated Notifications & Alerts

## Key Success Metrics

### Technical Metrics
- **Performance**: Page load times < 2 seconds
- **Availability**: 99.9% uptime
- **Security**: Zero security incidents
- **Code Quality**: Maintainable, documented code

### Business Metrics
- **User Adoption**: Active user engagement
- **Data Accuracy**: Reliable mission and commodity tracking
- **Efficiency**: Reduced manual processes
- **Scalability**: Support for growing user base

---

*Last Updated: September 2025*
*Version: 1.0*
*Framework: CodeIgniter 4*
*Database: Supabase PostgreSQL*