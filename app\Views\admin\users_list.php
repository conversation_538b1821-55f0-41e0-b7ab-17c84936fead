<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<style>
    /* Custom Table Controls Styling */
    .table-controls {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-controls input[type="text"] {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 0.5rem;
    }

    .table-controls input[type="text"]:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table-pagination {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }

<style>
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .user-details {
        flex: 1;
    }
    
    .user-name {
        font-weight: 600;
        margin-bottom: 2px;
    }
    
    .user-email {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .role-badges {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
    }
    
    .role-badge {
        font-size: 0.75rem;
        padding: 2px 6px;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
        border-radius: 12px;
    }
    
    .action-buttons {
        display: flex;
        gap: 4px;
    }
    
    .btn-action {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
    }
    
    .admin-table {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .admin-table table {
        margin-bottom: 0;
    }
    
    .admin-table th {
        background: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }
    
    .admin-table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .admin-table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .bulk-actions {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: none;
    }
    
    .bulk-actions.show {
        display: block;
    }
    
    .filters-section {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .search-box {
        position: relative;
    }
    
    .search-box .fas {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }
    
    .search-box input {
        padding-left: 40px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>


<!-- Bulk Actions -->
<div class="bulk-actions" id="bulkActions">
    <div class="row align-items-center">
        <div class="col-md-6">
            <span class="fw-bold">
                <span id="selectedCount">0</span> users selected
            </span>
        </div>
        <div class="col-md-6 text-end">
            <button class="btn btn-success btn-sm me-2" id="bulkActivateBtn">
                <i class="fas fa-check me-1"></i>Activate
            </button>
            <button class="btn btn-warning btn-sm me-2" id="bulkDeactivateBtn">
                <i class="fas fa-pause me-1"></i>Deactivate
            </button>
            <button class="btn btn-danger btn-sm" id="bulkDeleteBtn">
                <i class="fas fa-trash me-1"></i>Delete
            </button>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="admin-table">
    <div class="table-responsive">
        <table id="usersTable" class="table table-hover mb-0">
        <thead>
            <tr>
                <th width="40">
                    <input type="checkbox" class="form-check-input" id="selectAllHeader">
                </th>
                <th data-sortable>User</th>
                <th data-sortable>Username</th>
                <th>Roles</th>
                <th data-sortable>Status</th>
                <th data-sortable>Joined</th>
                <th data-sortable>Last Login</th>
                <th width="120">Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($users)): ?>
                <?php foreach ($users as $user): ?>
                <tr>
                    <td><input type="checkbox" class="form-check-input row-select" value="<?= $user['id'] ?>"></td>
                    <td>
                        <div class="user-info">
                            <img src="<?= base_url('public/assets/images/default-avatar.svg') ?>" 
                                 alt="<?= esc($user['fullname']) ?>" class="user-avatar">
                            <div class="user-details">
                                <div class="user-name"><?= esc($user['fullname']) ?></div>
                                <div class="user-email"><?= esc($user['email']) ?></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="fw-bold"><?= esc($user['username']) ?></span>
                    </td>
                    <td>
                        <div class="role-badges">
                            <?php if (isset($user['is_admin']) && $user['is_admin'] === true): ?>
                                <span class="badge bg-danger role-badge">Admin</span>
                            <?php endif; ?>
                            <?php if (isset($user['is_supervisor']) && $user['is_supervisor'] === true): ?>
                                <span class="badge bg-warning role-badge">Supervisor</span>
                            <?php endif; ?>
                            <?php if (isset($user['is_buyer']) && $user['is_buyer'] === true): ?>
                                <span class="badge bg-info role-badge">Buyer</span>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <?php
                        $statusClass = match($user['status']) {
                            'active' => 'bg-success',
                            'inactive' => 'bg-secondary',
                            'suspended' => 'bg-danger',
                            'pending' => 'bg-warning',
                            default => 'bg-secondary'
                        };
                        ?>
                        <span class="badge <?= $statusClass ?> status-badge"><?= ucfirst(esc($user['status'])) ?></span>
                    </td>
                    <td>
                        <div><?= date('M j, Y', strtotime($user['created_at'])) ?></div>
                        <small class="text-muted"><?= date('g:i A', strtotime($user['created_at'])) ?></small>
                    </td>
                    <td>
                        <div class="text-muted">Never</div>
                        <small class="text-muted">-</small>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewUser(<?= $user['id'] ?>)" title="View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary btn-action" onclick="editUser(<?= $user['id'] ?>)" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteUser(<?= $user['id'] ?>)" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <h5>No Users Found</h5>
                            <p>No users have been created yet. Use the "Create User" button above to add your first user.</p>
                        </div>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
        </table>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Initialize enhanced table functionality using existing admin template pattern
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedTable('usersTable', {
        searchPlaceholder: 'Search users by name, email, username, or role...',
        exportFilename: 'users_report',
        rowsPerPage: 25
    });

    // Handle select all functionality for bulk actions
    const selectAllHeader = document.getElementById('selectAllHeader');
    const rowSelects = document.querySelectorAll('.row-select');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    function updateBulkActions() {
        const selected = document.querySelectorAll('.row-select:checked');
        if (selected.length > 0) {
            bulkActions.classList.add('show');
            selectedCount.textContent = selected.length;
        } else {
            bulkActions.classList.remove('show');
        }
    }

    // Select all functionality
    if (selectAllHeader) {
        selectAllHeader.addEventListener('change', function() {
            rowSelects.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    // Individual row selection
    rowSelects.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();

            // Update select all checkbox
            const allChecked = Array.from(rowSelects).every(cb => cb.checked);
            const someChecked = Array.from(rowSelects).some(cb => cb.checked);

            if (selectAllHeader) {
                selectAllHeader.checked = allChecked;
                selectAllHeader.indeterminate = someChecked && !allChecked;
            }
        });
    });

    // Bulk actions - Standard form submission
    document.getElementById('bulkActivateBtn')?.addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
        if (selected.length > 0) {
            AdminTemplate.confirmAction(`Activate ${selected.length} selected users?`, function() {
                submitBulkAction('activate', selected);
            });
        }
    });

    document.getElementById('bulkDeactivateBtn')?.addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
        if (selected.length > 0) {
            AdminTemplate.confirmAction(`Deactivate ${selected.length} selected users?`, function() {
                submitBulkAction('deactivate', selected);
            });
        }
    });

    document.getElementById('bulkDeleteBtn')?.addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
        if (selected.length > 0) {
            AdminTemplate.confirmAction(`Delete ${selected.length} selected users? This action cannot be undone.`, function() {
                submitBulkAction('delete', selected);
            });
        }
    });
});

// Enhanced table functionality implementation
function initializeEnhancedTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const config = {
        searchPlaceholder: options.searchPlaceholder || 'Search...',
        exportFilename: options.exportFilename || 'table_export',
        rowsPerPage: options.rowsPerPage || 25,
        ...options
    };

    // Add search functionality
    addTableSearch(tableId, config);

    // Add export functionality
    addExportButton(tableId, config);

    // Add pagination
    addTablePagination(tableId, config);

    // Initialize existing admin template table features
    AdminTemplate.initializeDataTable(tableId, {
        sortable: true,
        searchable: true
    });
}

function addTableSearch(tableId, config) {
    const table = document.getElementById(tableId);
    const tableContainer = table.closest('.table-responsive').parentNode;

    // Create search container
    const searchContainer = document.createElement('div');
    searchContainer.className = 'table-controls mb-3 d-flex justify-content-between align-items-center';

    // Create search input
    const searchDiv = document.createElement('div');
    searchDiv.className = 'd-flex align-items-center';
    searchDiv.innerHTML = `
        <label class="me-2 mb-0">Search:</label>
        <input type="text" id="${tableId}Search" class="form-control" style="width: 300px;" placeholder="${config.searchPlaceholder}">
    `;

    // Create export button
    const exportDiv = document.createElement('div');
    exportDiv.innerHTML = `
        <button type="button" id="${tableId}ExportExcel" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>Export to Excel
        </button>
    `;

    searchContainer.appendChild(searchDiv);
    searchContainer.appendChild(exportDiv);
    tableContainer.insertBefore(searchContainer, tableContainer.firstChild);

    // Add search functionality
    const searchInput = document.getElementById(`${tableId}Search`);
    searchInput.addEventListener('input', function() {
        filterTable(tableId, this.value.toLowerCase());
    });
}

function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    updateTableInfo(tableId);
}

function addExportButton(tableId, config) {
    const exportBtn = document.getElementById(`${tableId}ExportExcel`);
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportTableToExcel(tableId, config.exportFilename);
        });
    }
}

function exportTableToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            // Skip the checkbox column (first) and Actions column (last)
            if (cellIndex > 0 && cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                // Clean up status badges
                if (cell.querySelector('.badge')) {
                    cellText = cell.querySelector('.badge').textContent.trim();
                }
                rowData.push(cellText);
            }
        });

        if (rowData.length > 0) {
            data.push(rowData);
        }
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);

    // Set column widths for users table
    const colWidths = [
        { wch: 25 }, // User (name + email)
        { wch: 15 }, // Username
        { wch: 20 }, // Roles
        { wch: 12 }, // Status
        { wch: 15 }, // Joined
        { wch: 15 }  // Last Login
    ];
    ws['!cols'] = colWidths;

    // Style the header row
    const headerRange = XLSX.utils.decode_range(ws['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "2E7D32" } },
            alignment: { horizontal: "center" }
        };
    }

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Users Report");

    // Generate filename with current date
    const exportFilename = filename + '_' + new Date().toISOString().split('T')[0] + '.xlsx';

    // Save the file
    XLSX.writeFile(wb, exportFilename);

    AdminTemplate.showNotification('Excel file exported successfully!', 'success');
}

function addTablePagination(tableId, config) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    if (rows.length <= config.rowsPerPage) return; // No pagination needed for small tables

    let currentPage = 1;
    const rowsPerPage = config.rowsPerPage;
    const totalPages = Math.ceil(rows.length / rowsPerPage);

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'table-pagination mt-3 d-flex justify-content-between align-items-center';
    paginationContainer.innerHTML = `
        <div class="table-info">
            <span id="${tableId}TableInfo">Showing 1 to ${Math.min(rowsPerPage, rows.length)} of ${rows.length} entries</span>
        </div>
        <nav>
            <ul class="pagination mb-0" id="${tableId}TablePagination">
                <!-- Pagination buttons will be generated here -->
            </ul>
        </nav>
    `;

    table.parentNode.appendChild(paginationContainer);

    function showPage(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        rows.forEach((row, index) => {
            if (index >= start && index < end && row.style.display !== 'none') {
                row.style.display = '';
            } else if (row.style.display !== 'none') {
                row.style.display = 'none';
            }
        });

        updatePagination(tableId, page, totalPages, showPage);
        updateTableInfo(tableId);
    }

    // Initialize first page
    showPage(1);
}

function updatePagination(tableId, currentPage, totalPages, showPageCallback) {
    const pagination = document.getElementById(`${tableId}TablePagination`);
    pagination.innerHTML = '';

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>`;
    pagination.appendChild(prevLi);

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
    pagination.appendChild(nextLi);

    // Add click events
    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        if (e.target.classList.contains('page-link') && !e.target.parentNode.classList.contains('disabled')) {
            const page = parseInt(e.target.dataset.page);
            if (page >= 1 && page <= totalPages) {
                showPageCallback(page);
            }
        }
    });
}

function updateTableInfo(tableId) {
    const tableInfo = document.getElementById(`${tableId}TableInfo`);
    if (!tableInfo) return;

    const table = document.getElementById(tableId);
    const visibleRows = table.querySelectorAll('tbody tr[style=""], tbody tr:not([style])');
    const totalRows = table.querySelectorAll('tbody tr').length;

    tableInfo.textContent = `Showing ${visibleRows.length} of ${totalRows} entries`;
}

// Function to submit bulk actions using standard form submission
function submitBulkAction(action, userIds) {
    // Create a form to submit the bulk action
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= base_url('admin/users/bulk-action') ?>';

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '<?= csrf_token() ?>';
    csrfInput.value = '<?= csrf_hash() ?>';
    form.appendChild(csrfInput);

    // Add action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action;
    form.appendChild(actionInput);

    // Add user IDs
    userIds.forEach(function(userId) {
        const userInput = document.createElement('input');
        userInput.type = 'hidden';
        userInput.name = 'user_ids[]';
        userInput.value = userId;
        form.appendChild(userInput);
    });

    // Submit the form
    document.body.appendChild(form);
    form.submit();
}



function viewUser(id) {
    window.location.href = `<?= base_url('admin/users/') ?>${id}`;
}

function editUser(id) {
    window.location.href = `<?= base_url('admin/users/') ?>${id}/edit`;
}

function deleteUser(id) {
    AdminTemplate.confirmAction('Are you sure you want to delete this user? This action cannot be undone.', function() {
        // Create a form to submit DELETE request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= base_url('admin/users/') ?>${id}`;

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);

        // Add method override for DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    });
}
</script>
<?= $this->endSection() ?>
